@{
    ViewData["Title"] = "Employee Grid";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="display-4 text-center mb-4">Employee Management</h1>
            <p class="text-center text-muted mb-4">Manage and view employee data with advanced filtering, sorting, and grouping capabilities.</p>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>Employee Data Grid
                    </h5>
                </div>
                <div class="card-body">
                    <div id="employeeGrid"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $("#employeeGrid").kendoGrid({
                dataSource: {
                    transport: {
                        read: {
                            url: "@Url.Action("GetEmployees", "Home")",
                            dataType: "json"
                        }
                    },
                    pageSize: 15,
                    schema: {
                        model: {
                            id: "EmployeeId",
                            fields: {
                                EmployeeId: { type: "number" },
                                FirstName: { type: "string" },
                                LastName: { type: "string" },
                                Email: { type: "string" },
                                Department: { type: "string" },
                                Position: { type: "string" },
                                HireDate: { type: "date" },
                                Salary: { type: "number" },
                                City: { type: "string" },
                                Country: { type: "string" }
                            }
                        }
                    }
                },
                height: 600,
                sortable: true,
                pageable: {
                    refresh: true,
                    pageSizes: [10, 15, 25, 50],
                    buttonCount: 5
                },
                filterable: {
                    mode: "row"
                },
                resizable: true,
                reorderable: true,
                groupable: true,
                columnMenu: true,
                toolbar: ["excel", "pdf"],
                excel: {
                    fileName: "Employees.xlsx",
                    proxyURL: "/Home/Excel_Export_Save",
                    filterable: true
                },
                pdf: {
                    allPages: true,
                    avoidLinks: true,
                    paperSize: "A4",
                    margin: { top: "2cm", left: "1cm", right: "1cm", bottom: "1cm" },
                    landscape: true,
                    repeatHeaders: true,
                    template: $("#page-template").html(),
                    scale: 0.8
                },
                columns: [
                    {
                        field: "EmployeeId",
                        title: "ID",
                        width: 80,
                        filterable: false
                    },
                    {
                        field: "FirstName",
                        title: "First Name",
                        width: 130
                    },
                    {
                        field: "LastName",
                        title: "Last Name",
                        width: 130
                    },
                    {
                        field: "Email",
                        title: "Email",
                        width: 220,
                        template: '<a href="mailto:#= Email #">#= Email #</a>'
                    },
                    {
                        field: "Department",
                        title: "Department",
                        width: 130
                    },
                    {
                        field: "Position",
                        title: "Position",
                        width: 160
                    },
                    {
                        field: "HireDate",
                        title: "Hire Date",
                        width: 130,
                        format: "{0:MM/dd/yyyy}",
                        filterable: {
                            ui: "datepicker"
                        }
                    },
                    {
                        field: "Salary",
                        title: "Salary",
                        width: 130,
                        format: "{0:c}",
                        attributes: { style: "text-align: right;" },
                        filterable: {
                            ui: "numerictextbox"
                        }
                    },
                    {
                        field: "City",
                        title: "City",
                        width: 130
                    },
                    {
                        field: "Country",
                        title: "Country",
                        width: 110
                    }
                ]
            });
        });
    </script>
}

<!-- PDF Export Template -->
<script type="x/kendo-template" id="page-template">
    <div class="page-header">
        <div style="float: right">Page #: pageNum # of #: totalPages #</div>
        <h2>Employee Report</h2>
        <div style="clear: both;"></div>
    </div>
</script>
