﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome</h1>
    <p>Learn about <a href="https://learn.microsoft.com/aspnet/core">building Web apps with ASP.NET Core</a>.</p>

    <div class="mt-4">
        <button id="showGridBtn" class="btn btn-primary btn-lg">Show Employee Grid</button>
    </div>
</div>

<div class="mt-5" id="gridContainer" style="display: none;">
    <h2 class="text-center mb-4">Employee Data</h2>
    <div id="employeeGrid"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            var gridInitialized = false;

            $("#showGridBtn").click(function () {
                var container = $("#gridContainer");

                if (container.is(":visible")) {
                    container.hide();
                    $(this).text("Show Employee Grid");
                } else {
                    container.show();
                    $(this).text("Hide Employee Grid");

                    // Initialize grid only once
                    if (!gridInitialized) {
                        initializeGrid();
                        gridInitialized = true;
                    }
                }
            });

            function initializeGrid() {
                $("#employeeGrid").kendoGrid({
                    dataSource: {
                        transport: {
                            read: {
                                url: "@Url.Action("GetEmployees", "Home")",
                                dataType: "json"
                            }
                        },
                        pageSize: 10,
                        schema: {
                            model: {
                                id: "EmployeeId",
                                fields: {
                                    EmployeeId: { type: "number" },
                                    FirstName: { type: "string" },
                                    LastName: { type: "string" },
                                    Email: { type: "string" },
                                    Department: { type: "string" },
                                    Position: { type: "string" },
                                    HireDate: { type: "date" },
                                    Salary: { type: "number" },
                                    City: { type: "string" },
                                    Country: { type: "string" }
                                }
                            }
                        }
                    },
                    height: 500,
                    sortable: true,
                    pageable: {
                        refresh: true,
                        pageSizes: true,
                        buttonCount: 5
                    },
                    filterable: true,
                    resizable: true,
                    reorderable: true,
                    groupable: true,
                    columns: [
                        {
                            field: "EmployeeId",
                            title: "ID",
                            width: 80
                        },
                        {
                            field: "FirstName",
                            title: "First Name",
                            width: 120
                        },
                        {
                            field: "LastName",
                            title: "Last Name",
                            width: 120
                        },
                        {
                            field: "Email",
                            title: "Email",
                            width: 200
                        },
                        {
                            field: "Department",
                            title: "Department",
                            width: 120
                        },
                        {
                            field: "Position",
                            title: "Position",
                            width: 150
                        },
                        {
                            field: "HireDate",
                            title: "Hire Date",
                            width: 120,
                            format: "{0:MM/dd/yyyy}"
                        },
                        {
                            field: "Salary",
                            title: "Salary",
                            width: 120,
                            format: "{0:c}"
                        },
                        {
                            field: "City",
                            title: "City",
                            width: 120
                        },
                        {
                            field: "Country",
                            title: "Country",
                            width: 100
                        }
                    ]
                });
            }
        });
    </script>
}
