﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome</h1>
    <p>Learn about <a href="https://learn.microsoft.com/aspnet/core">building Web apps with ASP.NET Core</a>.</p>

    <div class="mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-body">
                        <h5 class="card-title">Employee Management System</h5>
                        <p class="card-text">Access and manage employee data with our powerful Kendo Grid interface featuring sorting, filtering, grouping, and export capabilities.</p>
                        <a href="@Url.Action("EmployeeGrid", "Home")" class="btn btn-primary btn-lg">
                            <i class="fas fa-table me-2"></i>View Employee Grid
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <h3>Features</h3>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-sort fa-2x text-primary mb-3"></i>
                                <h6>Advanced Sorting</h6>
                                <p class="small">Sort by any column with multi-column sorting support</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-filter fa-2x text-success mb-3"></i>
                                <h6>Powerful Filtering</h6>
                                <p class="small">Filter data with row-based filters and advanced options</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-2x text-info mb-3"></i>
                                <h6>Export Options</h6>
                                <p class="small">Export data to Excel and PDF formats</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
