{"GlobalPropertiesHash": "7ssoaDTwKiVD74FB583X7yJaYylklitCr6A/IFPosow=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["6HwabLnv+wsbLPTSO8q7YKP7FtqTdIymDCIeUZrvlpM=", "354SyzoPjC8pFiX/b56TMKw8WDQioLG6YNjcjc+TtAY=", "NSFKRuYZtksJYqZupvfj8EfnuwEes0xn9s5JVoxJQPI=", "v17sw/xfXgCZPa4tInWmH9nPmWJJi/tLr//PxdlJovc=", "LVBjRVep4N93r0AOjaJd36mWWNH/EjsSSjbZYu/Fnlw=", "2KXpuVOEbJuVPreMJTLSdf9JxJsYEsAxyWkM+xhkdls=", "KC9TYTG7isQ4oMxlVQSBcDJTC4P2BdHYKcCYNoSSHOw=", "vrZrY4ClF4WFlZNqjjaALlGRJjTpcT5CWRdUh8KoDfg=", "p5vgqHj1JCHL5xIGYG8YPQDLheVHGWTdXfjYe3FVOI0=", "h9SJCxAAmCTVGJPVDSmU9d1h7WHZezbT1y5eoYCemhM=", "DyhkeoEsRgN9jARkJ7PxqaTG/ZDN+zleyRaTqDUl6/o=", "0dcstaf4Bp9KsPZ872HDW52tEeF8IICmbK3MUf3itzQ=", "Z5OozfSDnT2rTHNWkDmjHCvEHnDKYb0rTckMsrGB/JQ=", "EBuLSxj7sfiuqxkFgfdfx28w7T1cSf8EpBECoi5RCgE=", "g1DKlaOmLgH/bQcjFdhW9FNisx3zzystQNfJC+JFPaw=", "n217LmwoaTGeyq+IVtWS41dZerMtJMoRaReHc7sGNKk=", "LoZh4F9wnTh37UICNZHWWYZd9A7miryazbAOGPOlT18=", "8XL1oJbmqaq6s0XetJrb4WZ9w/Q3Nw0RDkVnVs1MoQ4=", "zMQFGEwtf8MnFEiHsPaZCjnxoWSZGyKe4uo90j9KtUY=", "hNSiJ5sYThfTgXBpz9Hw0Jrm7fJ1IY0/6EElJSwY01s=", "l4KvhdRheBm7beduaVs2M8w4deJlUbGZIIBF9b4NOyg=", "u5YJynertuqMMslPCRSCUqmQU/HUtUWQrPZSyzhZPeQ=", "BuLV+D0xwcpeS6O1MonQfTV7Y7DZGJvspsTD93O0AOU=", "FmidPFVSEaqGPe5N2wdgSMfGawCczNg2fpDt+MiSxVo=", "olXIrdCA2jZDUQR3SGzR3/GKTzBAvqg6jrKYwAGpBlE=", "HLVtWLWKEO8op7SufraQGXyxxa1wmmYtf61NUXvj/rA=", "8AlRK12ManUbyzBHmFW8l/omxeklf4hdOF/LF3bknMM=", "Abxqle419bFkeUttLt/o+TtyVlA4ey3ei5kqt6IdI6U=", "h2pBXidyehvDJxqvbjbvJYnAPbvyGO+5ognYRtg9eks=", "W5xf96vFPOBPUBmma4iSWivhzjPylBBQdbSd497lQng=", "q59vG2NzQCE5d4SpyyNR7OKB2Nce2gZNhuH3USduJEc=", "2bxmnotf1YVJlGx8bJ2mWIqONFzaOi+pVAOxLyLG+0E=", "jvgjzc5ymw0a2WlUP9y+DYsKlBvAc2uLpT5vlmqfVNE=", "Udfk0PPn6qth0KP8gm1+JyW3tO5iWnrkUDV5UA1YhsM=", "AUy0U6/ykbcw5r7u4GAgJ5QGyKvSwIooszQTEiHqj/I=", "UMbkHxb104p1lVNHbUoxrpo+z5jDBSgYzpA16WDTaAE=", "ZsXKmgqfq2NmbUmfysZ2PLnRAwyCBzVmebEuWNJzCOQ=", "KEB3i/9KyzR4IJ9GRD1SvgY60YiadoXCFEfTvWMLZQA=", "h4Hjfp/xum24OGAZsTWQTyp3bYi+v3OtyVksnbcgy28=", "8qZ/RPKG1mpBdFy36Auj538S/Kf4ArfMRVfvary3iFk=", "iTySTLzJNyW1ysKL9TXFiM90KfO6RCS8vqN17fXBl7Y=", "fz2yHkqlebvEsV0AWB0x4hG4/TStBH5w4Wk3HJohuRg=", "NvkQfkehd1NwhJ/pCCHw7Bgyl5jhOiupI1PUrxRKvCQ=", "iFCxNTBjHJFM7CGz5rEG9HJ9wvzmu9V8NazlJJYiKdM=", "V7VcrGLOhfB1OBXvPzq+xiKLignfxQRaBhH2i0qGpm8=", "PUzH+vEGrrzENPnstVODiWu8FixzQcE3wB8NyW9Icr8=", "R8/G1X0GjSZBqu5tw31EbQeiek1J7be5vGZvYYMgugM=", "yn6qZ66r9jSpfsbYGqb+W8K/8lsrO/2wNvBXoeNkUWY=", "+mhC7cILXYqUoft1ZVV+IEO0DQgZHRr6KaCWo7ViHBg=", "bj1BfiMjTVADev6AemlNd3F4ZbqddCo2VWR61MjypAs=", "Sw4HtVFa3NFi266wfC3HQ0ge4783hQS72uYkZiBr9xU=", "cDLkcLerH2v2XDYF6GYczATkBZJErAfzcBNb6wLOia4=", "YvSbbxxKxlcJNoRlYEhpIg7kzazaGNFQFjWOCPs8zD4=", "54wy4e3shOXIa+CAy2/wQQtUJh9CzWEYK/9MlTd9X4k=", "wRgZ9sUPC3L5OhW1nUMSVAW+kY/XwSX0FkdCjQGWGSM=", "KQE1LaJueZkBg24AqVJ/nuh+NBOcjfIBkovCqW6PVx8=", "83wOuf8Xxu1MGQx10V0U3qwYJbRb2A09HnKVZppbwds=", "rwFVOGX0tWWC2OuZUiTSNPDpLIDZ5+OyptZ8b/DJUHw=", "rA6pUGWVsj7RPTJOoeINGVc2n2jV7xQrdANcQGXuus4=", "t9/7D+mZ6CfBRsnFqmt70FRtTIl/0IZeIhTPxT55GAI=", "m+BYsM9tjKfIWojELYgUyRg/HRxcwyLvjDQayBIjn5Y=", "HflFtztsbxWo9zkhPK/ZCObKfebfTImHdLd8Wjo/iME=", "JZZ+kc+5Mz+Ts7IR1P3SjUdvVX/X1Xe47U0yWzkE2V0=", "CBIfeQJp2RoSoJI+XqEJEzrAO2veJnL17wpYo59VI2o=", "Hk5zkX2qdf9lgFozR/F4zmRU2MO8oYbmBtcbeIZiDPo=", "KpKPqJcfwCmfZOncVJyWWFPdo/a6b+f8pa3B5RlsZRE=", "oJbvct4rlwxZqZ6Gd4jm44TRvAyCYVgkaaI6X20LP3o=", "Z/yFk6d9KUYA99zGkQkOjcQoq3VZjtTOx8Oyvrg97tU=", "Uzj/22dLUhujB6ydOdhGvqQNlU0vKiS2qEMhCvWpwRQ=", "wup6V5FSR6GuRfz/4ruuP48slnQby/54GGILwdrOXMQ="], "CachedAssets": {"t9/7D+mZ6CfBRsnFqmt70FRtTIl/0IZeIhTPxT55GAI=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-11T07:50:20.4712384+00:00"}, "rA6pUGWVsj7RPTJOoeINGVc2n2jV7xQrdANcQGXuus4=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-09-11T07:50:20.3125407+00:00"}, "rwFVOGX0tWWC2OuZUiTSNPDpLIDZ5+OyptZ8b/DJUHw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-09-11T07:50:20.3110418+00:00"}, "83wOuf8Xxu1MGQx10V0U3qwYJbRb2A09HnKVZppbwds=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-09-11T07:50:20.3095246+00:00"}, "KQE1LaJueZkBg24AqVJ/nuh+NBOcjfIBkovCqW6PVx8=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-11T07:50:20.4732963+00:00"}, "wRgZ9sUPC3L5OhW1nUMSVAW+kY/XwSX0FkdCjQGWGSM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-09-11T07:50:20.3175555+00:00"}, "54wy4e3shOXIa+CAy2/wQQtUJh9CzWEYK/9MlTd9X4k=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-09-11T07:50:20.3165557+00:00"}, "YvSbbxxKxlcJNoRlYEhpIg7kzazaGNFQFjWOCPs8zD4=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-09-11T07:50:20.3155552+00:00"}, "cDLkcLerH2v2XDYF6GYczATkBZJErAfzcBNb6wLOia4=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-09-11T07:50:20.3145563+00:00"}, "Sw4HtVFa3NFi266wfC3HQ0ge4783hQS72uYkZiBr9xU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-11T07:50:20.4763425+00:00"}, "bj1BfiMjTVADev6AemlNd3F4ZbqddCo2VWR61MjypAs=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-11T07:50:20.4753078+00:00"}, "+mhC7cILXYqUoft1ZVV+IEO0DQgZHRr6KaCWo7ViHBg=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-11T07:50:20.4743074+00:00"}, "yn6qZ66r9jSpfsbYGqb+W8K/8lsrO/2wNvBXoeNkUWY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-11T07:50:20.4702277+00:00"}, "R8/G1X0GjSZBqu5tw31EbQeiek1J7be5vGZvYYMgugM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-09-11T07:50:20.4561207+00:00"}, "PUzH+vEGrrzENPnstVODiWu8FixzQcE3wB8NyW9Icr8=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-09-11T07:50:20.4538258+00:00"}, "V7VcrGLOhfB1OBXvPzq+xiKLignfxQRaBhH2i0qGpm8=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-09-11T07:50:20.4528265+00:00"}, "iFCxNTBjHJFM7CGz5rEG9HJ9wvzmu9V8NazlJJYiKdM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-09-11T07:50:20.4518208+00:00"}, "NvkQfkehd1NwhJ/pCCHw7Bgyl5jhOiupI1PUrxRKvCQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-09-11T07:50:20.4508142+00:00"}, "fz2yHkqlebvEsV0AWB0x4hG4/TStBH5w4Wk3HJohuRg=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-09-11T07:50:20.449313+00:00"}, "iTySTLzJNyW1ysKL9TXFiM90KfO6RCS8vqN17fXBl7Y=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-09-11T07:50:20.4473096+00:00"}, "8qZ/RPKG1mpBdFy36Auj538S/Kf4ArfMRVfvary3iFk=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-09-11T07:50:20.4453101+00:00"}, "h4Hjfp/xum24OGAZsTWQTyp3bYi+v3OtyVksnbcgy28=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-09-11T07:50:20.4443105+00:00"}, "KEB3i/9KyzR4IJ9GRD1SvgY60YiadoXCFEfTvWMLZQA=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-09-11T07:50:20.4413114+00:00"}, "ZsXKmgqfq2NmbUmfysZ2PLnRAwyCBzVmebEuWNJzCOQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-09-11T07:50:20.4403035+00:00"}, "UMbkHxb104p1lVNHbUoxrpo+z5jDBSgYzpA16WDTaAE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-09-11T07:50:20.4377901+00:00"}, "AUy0U6/ykbcw5r7u4GAgJ5QGyKvSwIooszQTEiHqj/I=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-09-11T07:50:20.4355495+00:00"}, "Udfk0PPn6qth0KP8gm1+JyW3tO5iWnrkUDV5UA1YhsM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-09-11T07:50:20.4324604+00:00"}, "jvgjzc5ymw0a2WlUP9y+DYsKlBvAc2uLpT5vlmqfVNE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-09-11T07:50:20.4304524+00:00"}, "2bxmnotf1YVJlGx8bJ2mWIqONFzaOi+pVAOxLyLG+0E=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-09-11T07:50:20.4279388+00:00"}, "q59vG2NzQCE5d4SpyyNR7OKB2Nce2gZNhuH3USduJEc=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-09-11T07:50:20.426439+00:00"}, "W5xf96vFPOBPUBmma4iSWivhzjPylBBQdbSd497lQng=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-09-11T07:50:20.4234368+00:00"}, "h2pBXidyehvDJxqvbjbvJYnAPbvyGO+5ognYRtg9eks=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-09-11T07:50:20.4224319+00:00"}, "Abxqle419bFkeUttLt/o+TtyVlA4ey3ei5kqt6IdI6U=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-09-11T07:50:20.4179056+00:00"}, "8AlRK12ManUbyzBHmFW8l/omxeklf4hdOF/LF3bknMM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-09-11T07:50:20.4159037+00:00"}, "HLVtWLWKEO8op7SufraQGXyxxa1wmmYtf61NUXvj/rA=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-09-11T07:50:20.4149018+00:00"}, "olXIrdCA2jZDUQR3SGzR3/GKTzBAvqg6jrKYwAGpBlE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-09-11T07:50:20.4139021+00:00"}, "FmidPFVSEaqGPe5N2wdgSMfGawCczNg2fpDt+MiSxVo=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-09-11T07:50:20.4116119+00:00"}, "BuLV+D0xwcpeS6O1MonQfTV7Y7DZGJvspsTD93O0AOU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-09-11T07:50:20.4106014+00:00"}, "u5YJynertuqMMslPCRSCUqmQU/HUtUWQrPZSyzhZPeQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-09-11T07:50:20.4090893+00:00"}, "l4KvhdRheBm7beduaVs2M8w4deJlUbGZIIBF9b4NOyg=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-09-11T07:50:20.4081926+00:00"}, "hNSiJ5sYThfTgXBpz9Hw0Jrm7fJ1IY0/6EElJSwY01s=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-09-11T07:50:20.4060879+00:00"}, "zMQFGEwtf8MnFEiHsPaZCjnxoWSZGyKe4uo90j9KtUY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-09-11T07:50:20.4050847+00:00"}, "8XL1oJbmqaq6s0XetJrb4WZ9w/Q3Nw0RDkVnVs1MoQ4=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-09-11T07:50:20.4040844+00:00"}, "LoZh4F9wnTh37UICNZHWWYZd9A7miryazbAOGPOlT18=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-09-11T07:50:20.4030836+00:00"}, "n217LmwoaTGeyq+IVtWS41dZerMtJMoRaReHc7sGNKk=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-09-11T07:50:20.4020754+00:00"}, "g1DKlaOmLgH/bQcjFdhW9FNisx3zzystQNfJC+JFPaw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-09-11T07:50:20.4010654+00:00"}, "EBuLSxj7sfiuqxkFgfdfx28w7T1cSf8EpBECoi5RCgE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-09-11T07:50:20.3995572+00:00"}, "Z5OozfSDnT2rTHNWkDmjHCvEHnDKYb0rTckMsrGB/JQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-09-11T07:50:20.3985867+00:00"}, "0dcstaf4Bp9KsPZ872HDW52tEeF8IICmbK3MUf3itzQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-09-11T07:50:20.3975888+00:00"}, "DyhkeoEsRgN9jARkJ7PxqaTG/ZDN+zleyRaTqDUl6/o=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-09-11T07:50:20.3965469+00:00"}, "h9SJCxAAmCTVGJPVDSmU9d1h7WHZezbT1y5eoYCemhM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-09-11T07:50:20.3953276+00:00"}, "p5vgqHj1JCHL5xIGYG8YPQDLheVHGWTdXfjYe3FVOI0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-09-11T07:50:20.3943385+00:00"}, "vrZrY4ClF4WFlZNqjjaALlGRJjTpcT5CWRdUh8KoDfg=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-09-11T07:50:20.3923226+00:00"}, "KC9TYTG7isQ4oMxlVQSBcDJTC4P2BdHYKcCYNoSSHOw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-09-11T07:50:20.3913226+00:00"}, "2KXpuVOEbJuVPreMJTLSdf9JxJsYEsAxyWkM+xhkdls=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-09-11T07:50:20.3903153+00:00"}, "LVBjRVep4N93r0AOjaJd36mWWNH/EjsSSjbZYu/Fnlw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-09-11T07:50:20.3888088+00:00"}, "v17sw/xfXgCZPa4tInWmH9nPmWJJi/tLr//PxdlJovc=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-09-11T07:50:20.3888088+00:00"}, "NSFKRuYZtksJYqZupvfj8EfnuwEes0xn9s5JVoxJQPI=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\js\\site.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-09-11T07:50:20.3075276+00:00"}, "354SyzoPjC8pFiX/b56TMKw8WDQioLG6YNjcjc+TtAY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\favicon.ico", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-11T07:50:20.4722377+00:00"}, "6HwabLnv+wsbLPTSO8q7YKP7FtqTdIymDCIeUZrvlpM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\css\\site.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-09-11T07:50:20.3065237+00:00"}}, "CachedCopyCandidates": {}}