{"GlobalPropertiesHash": "7ssoaDTwKiVD74FB583X7yJaYylklitCr6A/IFPosow=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["BjqiYwRWzVlvW9Gmj4NZaU0HeaU2hM/cUsn5VUGYUIo=", "YXRA+eY9HvP4q4papkpTbo3KP+Bp1uSfs2GWMPCucko=", "Z8t22iGszVxTco7quVwhMhd7xozcGs58e17P0gFHNy0=", "9ga111wDqEltHa8epK/YDDUvEUAEWGnm+5g3OJsRdXI=", "pcutGb1b4Kx3kjM0zYABTckSl8LGKLZgwFhXre2JoWE=", "XW5gsGjjNZHNtCaDn+K2l41g/PWg5spc9Sy4oixUbzE=", "52HATon9LSmzeUkyfBNR51VXm47XzKEvy/P4vApVa9Q=", "EVtzk5n9EQk/UtOcuHs7k/zUjJyxNSMpA9TsJyv+V+c=", "AgZuW9a/P1tHTgVNjvlzqEQFEh9l7BgcKUxaAUf8Oyg=", "Lzwm7W+YAjjsoSaI/pNxrsMuNt29Jc8UyMci62qZsKQ=", "M6Exox2HRONemm6uuUQdkPugnQzjcb0mbHGxWFDdZ38=", "H+vhTjFSm1B+jjV+RgoXJYDsvrZSEGFqvrX9dhbQhWE=", "rn6hnAT0l6C0MTc/+vpJtNmVI97mKQ936LPBrUmQmv4=", "GyE07ULyOwLBS3XwKq4BCUtU46F4dRAkhuyEGInhfkU=", "HaqQQF1hL0yB/amHumZkANBuamP2zTulPke7pK2R6Io=", "mmJVsC5phRlXstRFCAlRfOuXAxwnKNUjQkUc6LSy8Ic=", "2ROkAAPFBprTU2DH4BEqaAHW8S2VB7eIO/SlRDpJLY0=", "XNsHCDj0oyngVWldl+jbTOZfAvDvUWHV/pZ9DwGjK68=", "bejQM209vecP/L7M4tKmcSeZyI7bfK7mtGAvKDyPSyk=", "MXJ1n9gb4YoruLo2xWefAHUH1drXlxfWxN0z1cZeR+s=", "wqGDmLzGT8pA/lVnXIdAFcCpCNfKIYo+6Dw8j9q0ajA=", "DQ2Y7QJk/KKXJrefZ2/mVBbmMBepKPUGdyBuysYoaTw=", "kUObNlIF0/RiOmqL6CeAfAZ4lHciWqULoxK+1GPD+to=", "FxJum0UWPZyDTPe+ctaBwPVKvGID2/wzrf0aF+gaJC8=", "deNxCoFMb2bK0bZyptUJ0BeIkZzBombdxGaxdbLwIjw=", "8SXJaxyp1sV8Fck8MqDNttsPMDv2ME+qaUAQZrgZjVo=", "9WxPIdGKiGbj2g96pD8xnm6O9+pAA5TmGgTmDivDRUM=", "ltwlkvBh8+3acss7j7+zdiinZznWh/mhzu6ilbnmbLk=", "iBVGcg4Ma/V7IqD7UPXBVwrttC63Lzsp5gjrvcfob14=", "YBMqIsHGroPeu1+X4gv9bCxpCZBeYzYR1vMy1tkZQTA=", "evgRcCOE1c2htKs6Fw9PCSFQpfPH33oKzMC8wiuLD7M=", "ioVxyJGf6z0idf9O+1XmB4yMVl6WJ0Y9QgV82VE0GTE=", "FoH5IeeR9QZ54u1knOE3c69Cxhu4jM3EE6q6GKlV45k=", "SIcabd438JTt0+PWC7euswUN7IeYLyKktemeJDBPY9I=", "SwpYLbf7KQnXfNXdqNgVXIE1NGNRV4ALwhHkFYxEXRk=", "doEeDKCyImkF5uvVAKsBxGh7bjKuzxIwRvMaUmdeMKU=", "8QihaqZuyUTzewgLzmCq++gKT16qe+5b96+dehVqjfw=", "IpjDET9c3cScFS52BypQKU5x9w2OcNg1pANhVniPRaQ=", "FbVOVQpS36uDDSjyR9QyEvMpc/aaL8L/g66uv15MxOM=", "H824bsFtH/Omd/qKO3MeVeRJGJERlym77LtNPNsp3Qo=", "7mPf5pCCOuwOlwk+D4SFeXlK21rHzzP8lRsU3ZUARUE=", "cIeN2vLBQi+7Rqf7OE3iXuTESaMIX6s9hSmmyYGJv5Q=", "FLqCOT07PrVN62IHPc5AYqh3QVC7NkGjJ7XyCiEMj3Q=", "BZJD23BEpVQrXwsBspA9DT/7eimDSqLxDhiCKNOuXEc=", "fOF0CGtSM6zkfpA6ql/e/ytRDtkOe7JA9Vl2anUKvHo=", "I7c406beCz7MsudHIgQn+E6UjLLZIMSvnMfD9m8RWdQ=", "EzqRKGQ8PRQBsAgOjcRxv5UyKmwv/K7+XYbcqbrvJCU=", "93Xe+55r6jvQ6UvNMm7tEKOfvwlOG1XiHGm9aed19z0=", "aLNpKKfDZHgtFkklZ9HIiF5MyUcXKqF3tgASmPS+Iaw=", "7umUdnmY0V6ijoqn113IgXSrOXehbVvjfoBoY8s8nBs=", "YQA2YSpT7vuCmYMyQ90HmMP3+B8eZN+ghZTouqIcntY=", "HOWld7VaPzJB2llcVEo7PQRIWK67+o2qdjD8lG+flWY=", "DnYgXrhEkzXuZ5aG0+lX84ERIpbe9EngNBKX4OBkiAU=", "7sWVO0v4CHjrAcnKudsWS9R+UbdkafWFJT9oJPHByvY=", "xLKDyMqqVvxUcRdbNyYm5DSl105fInYhjSGCj7M5TE0=", "JxteEROyiQKRcZA0y1QbpGZg3PI+SITy0410nKsOox0=", "Q/+x9bqUspRbgTKYa7+dMW7DcS9dw/KDRwPXBUHUwhU=", "1tZmyIee1eCNY78/F+QYa81hAylj4/dd2gYBXGO9Upc=", "9L4x4pAN+6+56KoEZmNGKVJvcFQM9ywqofUkhX0heYA=", "02ic0AEAvGL32+fdEXq1wvCh9wt460lOJO4XCCngDJw=", "m+BYsM9tjKfIWojELYgUyRg/HRxcwyLvjDQayBIjn5Y=", "HflFtztsbxWo9zkhPK/ZCObKfebfTImHdLd8Wjo/iME=", "AJmqf54/Ti+gkFUtb1tgULbsSSlCxyTBB+jy0v5G9+c=", "l0Enwhm03Sa0Rc8AiUKDMhP5IvOS9ZfqTl8INwtz0pM=", "f7RYAmTKMkY3eVN4kUfqFl4zjAlT33zJjVeUV6ljEyg=", "36MKIfL3md4k4vp8CzscC3GUD3O3BRfhjka7b5t8WUI=", "n/Z3Aas/hmStBc0fQUQrfcBNYnJumdHya0A7pSETAVo=", "K2rK+YYmFHD8gidIVPe9TJZNY4CLIfmkQOrxjfYGwfg=", "xFRioDC7jq/1rQqiWAB9K6PBmcmQaLAZ7YFbVjBGMag=", "pLILRqXKUuQAo04k3RwkHV9x5lYh/t0fDtRIX1ajewI="], "CachedAssets": {"BjqiYwRWzVlvW9Gmj4NZaU0HeaU2hM/cUsn5VUGYUIo=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\css\\site.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-09-11T07:50:20.3065237+00:00"}, "YXRA+eY9HvP4q4papkpTbo3KP+Bp1uSfs2GWMPCucko=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\favicon.ico", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-11T07:50:20.4722377+00:00"}, "Z8t22iGszVxTco7quVwhMhd7xozcGs58e17P0gFHNy0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\js\\site.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-09-11T07:50:20.3075276+00:00"}, "9ga111wDqEltHa8epK/YDDUvEUAEWGnm+5g3OJsRdXI=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-09-11T07:50:20.3888088+00:00"}, "pcutGb1b4Kx3kjM0zYABTckSl8LGKLZgwFhXre2JoWE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-09-11T07:50:20.3888088+00:00"}, "XW5gsGjjNZHNtCaDn+K2l41g/PWg5spc9Sy4oixUbzE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-09-11T07:50:20.3903153+00:00"}, "52HATon9LSmzeUkyfBNR51VXm47XzKEvy/P4vApVa9Q=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-09-11T07:50:20.3913226+00:00"}, "EVtzk5n9EQk/UtOcuHs7k/zUjJyxNSMpA9TsJyv+V+c=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-09-11T07:50:20.3923226+00:00"}, "AgZuW9a/P1tHTgVNjvlzqEQFEh9l7BgcKUxaAUf8Oyg=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-09-11T07:50:20.3943385+00:00"}, "Lzwm7W+YAjjsoSaI/pNxrsMuNt29Jc8UyMci62qZsKQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-09-11T07:50:20.3953276+00:00"}, "M6Exox2HRONemm6uuUQdkPugnQzjcb0mbHGxWFDdZ38=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-09-11T07:50:20.3965469+00:00"}, "H+vhTjFSm1B+jjV+RgoXJYDsvrZSEGFqvrX9dhbQhWE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-09-11T07:50:20.3975888+00:00"}, "rn6hnAT0l6C0MTc/+vpJtNmVI97mKQ936LPBrUmQmv4=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-09-11T07:50:20.3985867+00:00"}, "GyE07ULyOwLBS3XwKq4BCUtU46F4dRAkhuyEGInhfkU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-09-11T07:50:20.3995572+00:00"}, "HaqQQF1hL0yB/amHumZkANBuamP2zTulPke7pK2R6Io=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-09-11T07:50:20.4010654+00:00"}, "mmJVsC5phRlXstRFCAlRfOuXAxwnKNUjQkUc6LSy8Ic=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-09-11T07:50:20.4020754+00:00"}, "2ROkAAPFBprTU2DH4BEqaAHW8S2VB7eIO/SlRDpJLY0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-09-11T07:50:20.4030836+00:00"}, "XNsHCDj0oyngVWldl+jbTOZfAvDvUWHV/pZ9DwGjK68=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-09-11T07:50:20.4040844+00:00"}, "bejQM209vecP/L7M4tKmcSeZyI7bfK7mtGAvKDyPSyk=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-09-11T07:50:20.4050847+00:00"}, "MXJ1n9gb4YoruLo2xWefAHUH1drXlxfWxN0z1cZeR+s=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-09-11T07:50:20.4060879+00:00"}, "wqGDmLzGT8pA/lVnXIdAFcCpCNfKIYo+6Dw8j9q0ajA=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-09-11T07:50:20.4081926+00:00"}, "DQ2Y7QJk/KKXJrefZ2/mVBbmMBepKPUGdyBuysYoaTw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-09-11T07:50:20.4090893+00:00"}, "kUObNlIF0/RiOmqL6CeAfAZ4lHciWqULoxK+1GPD+to=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-09-11T07:50:20.4106014+00:00"}, "FxJum0UWPZyDTPe+ctaBwPVKvGID2/wzrf0aF+gaJC8=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-09-11T07:50:20.4116119+00:00"}, "deNxCoFMb2bK0bZyptUJ0BeIkZzBombdxGaxdbLwIjw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-09-11T07:50:20.4139021+00:00"}, "8SXJaxyp1sV8Fck8MqDNttsPMDv2ME+qaUAQZrgZjVo=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-09-11T07:50:20.4149018+00:00"}, "9WxPIdGKiGbj2g96pD8xnm6O9+pAA5TmGgTmDivDRUM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-09-11T07:50:20.4159037+00:00"}, "ltwlkvBh8+3acss7j7+zdiinZznWh/mhzu6ilbnmbLk=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-09-11T07:50:20.4179056+00:00"}, "iBVGcg4Ma/V7IqD7UPXBVwrttC63Lzsp5gjrvcfob14=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-09-11T07:50:20.4224319+00:00"}, "YBMqIsHGroPeu1+X4gv9bCxpCZBeYzYR1vMy1tkZQTA=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-09-11T07:50:20.4234368+00:00"}, "evgRcCOE1c2htKs6Fw9PCSFQpfPH33oKzMC8wiuLD7M=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-09-11T07:50:20.426439+00:00"}, "ioVxyJGf6z0idf9O+1XmB4yMVl6WJ0Y9QgV82VE0GTE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-09-11T07:50:20.4279388+00:00"}, "FoH5IeeR9QZ54u1knOE3c69Cxhu4jM3EE6q6GKlV45k=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-09-11T07:50:20.4304524+00:00"}, "SIcabd438JTt0+PWC7euswUN7IeYLyKktemeJDBPY9I=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-09-11T07:50:20.4324604+00:00"}, "SwpYLbf7KQnXfNXdqNgVXIE1NGNRV4ALwhHkFYxEXRk=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-09-11T07:50:20.4355495+00:00"}, "doEeDKCyImkF5uvVAKsBxGh7bjKuzxIwRvMaUmdeMKU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-09-11T07:50:20.4377901+00:00"}, "8QihaqZuyUTzewgLzmCq++gKT16qe+5b96+dehVqjfw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-09-11T07:50:20.4403035+00:00"}, "IpjDET9c3cScFS52BypQKU5x9w2OcNg1pANhVniPRaQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-09-11T07:50:20.4413114+00:00"}, "FbVOVQpS36uDDSjyR9QyEvMpc/aaL8L/g66uv15MxOM=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-09-11T07:50:20.4443105+00:00"}, "H824bsFtH/Omd/qKO3MeVeRJGJERlym77LtNPNsp3Qo=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-09-11T07:50:20.4453101+00:00"}, "7mPf5pCCOuwOlwk+D4SFeXlK21rHzzP8lRsU3ZUARUE=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-09-11T07:50:20.4473096+00:00"}, "cIeN2vLBQi+7Rqf7OE3iXuTESaMIX6s9hSmmyYGJv5Q=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-09-11T07:50:20.449313+00:00"}, "FLqCOT07PrVN62IHPc5AYqh3QVC7NkGjJ7XyCiEMj3Q=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-09-11T07:50:20.4508142+00:00"}, "BZJD23BEpVQrXwsBspA9DT/7eimDSqLxDhiCKNOuXEc=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-09-11T07:50:20.4518208+00:00"}, "fOF0CGtSM6zkfpA6ql/e/ytRDtkOe7JA9Vl2anUKvHo=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-09-11T07:50:20.4528265+00:00"}, "I7c406beCz7MsudHIgQn+E6UjLLZIMSvnMfD9m8RWdQ=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-09-11T07:50:20.4538258+00:00"}, "EzqRKGQ8PRQBsAgOjcRxv5UyKmwv/K7+XYbcqbrvJCU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-09-11T07:50:20.4561207+00:00"}, "93Xe+55r6jvQ6UvNMm7tEKOfvwlOG1XiHGm9aed19z0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-11T07:50:20.4702277+00:00"}, "aLNpKKfDZHgtFkklZ9HIiF5MyUcXKqF3tgASmPS+Iaw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-11T07:50:20.4743074+00:00"}, "7umUdnmY0V6ijoqn113IgXSrOXehbVvjfoBoY8s8nBs=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-11T07:50:20.4753078+00:00"}, "YQA2YSpT7vuCmYMyQ90HmMP3+B8eZN+ghZTouqIcntY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-11T07:50:20.4763425+00:00"}, "HOWld7VaPzJB2llcVEo7PQRIWK67+o2qdjD8lG+flWY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-09-11T07:50:20.3145563+00:00"}, "DnYgXrhEkzXuZ5aG0+lX84ERIpbe9EngNBKX4OBkiAU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-09-11T07:50:20.3155552+00:00"}, "7sWVO0v4CHjrAcnKudsWS9R+UbdkafWFJT9oJPHByvY=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-09-11T07:50:20.3165557+00:00"}, "xLKDyMqqVvxUcRdbNyYm5DSl105fInYhjSGCj7M5TE0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-09-11T07:50:20.3175555+00:00"}, "JxteEROyiQKRcZA0y1QbpGZg3PI+SITy0410nKsOox0=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-11T07:50:20.4732963+00:00"}, "Q/+x9bqUspRbgTKYa7+dMW7DcS9dw/KDRwPXBUHUwhU=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-09-11T07:50:20.3095246+00:00"}, "1tZmyIee1eCNY78/F+QYa81hAylj4/dd2gYBXGO9Upc=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-09-11T07:50:20.3110418+00:00"}, "9L4x4pAN+6+56KoEZmNGKVJvcFQM9ywqofUkhX0heYA=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-09-11T07:50:20.3125407+00:00"}, "02ic0AEAvGL32+fdEXq1wvCh9wt460lOJO4XCCngDJw=": {"Identity": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "<PERSON><PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\Kendo\\Kendo\\wwwroot\\", "BasePath": "_content/Kendo", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-11T07:50:20.4712384+00:00"}}, "CachedCopyCandidates": {}}