using System.Diagnostics;
using Kendo.Models;
using Microsoft.AspNetCore.Mvc;

namespace Kendo.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        public IActionResult GetEmployees()
        {
            var employees = GenerateEmployeeData();
            return <PERSON><PERSON>(employees);
        }

        private List<Employee> GenerateEmployeeData()
        {
            var employees = new List<Employee>();
            var random = new Random();
            var firstNames = new[] { "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" };
            var last<PERSON><PERSON>s = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores", "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts" };
            var departments = new[] { "IT", "HR", "Finance", "Marketing", "Sales", "Operations", "Legal", "R&D", "Customer Service", "Administration" };
            var positions = new[] { "Manager", "Developer", "Analyst", "Coordinator", "Specialist", "Director", "Associate", "Senior Developer", "Team Lead", "Consultant" };
            var cities = new[] { "New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose", "Austin", "Jacksonville", "Fort Worth", "Columbus", "Charlotte", "San Francisco", "Indianapolis", "Seattle", "Denver", "Washington" };
            var countries = new[] { "USA", "Canada", "UK", "Germany", "France", "Australia", "Japan", "Brazil", "India", "Mexico" };

            for (int i = 1; i <= 50; i++)
            {
                employees.Add(new Employee
                {
                    EmployeeId = i,
                    FirstName = firstNames[random.Next(firstNames.Length)],
                    LastName = lastNames[random.Next(lastNames.Length)],
                    Email = $"{firstNames[random.Next(firstNames.Length)].ToLower()}.{lastNames[random.Next(lastNames.Length)].ToLower()}@company.com",
                    Department = departments[random.Next(departments.Length)],
                    Position = positions[random.Next(positions.Length)],
                    HireDate = DateTime.Now.AddDays(-random.Next(1, 3650)), // Random date within last 10 years
                    Salary = random.Next(40000, 150000),
                    City = cities[random.Next(cities.Length)],
                    Country = countries[random.Next(countries.Length)]
                });
            }

            return employees;
        }
    }
}
